'use client';

import { OceanScores } from "@/lib/types";
import { oceanDescriptions } from "@/lib/oceanQuestions";
import OceanRadarChart from '@/components/OceanRadarChart';

interface OceanChartProps {
  scores: OceanScores;
}

// Helper function to get trait level (Indonesian version)
function getTraitLevel(score: number): {
  level: 'Rendah' | 'Sedang' | 'Tinggi';
  color: string;
  description: string;
} {
  // OCEAN scores range from 5-25 (5 questions × 1-5 scale)
  if (score <= 12) {
    return {
      level: "Rendah",
      color: "border-red-300 bg-red-50 text-red-700",
      description: "Tingkat trait yang rendah"
    };
  } else if (score <= 18) {
    return {
      level: "Sedang",
      color: "border-yellow-300 bg-yellow-50 text-yellow-700",
      description: "Tingkat trait yang sedang"
    };
  } else {
    return {
      level: "Tinggi",
      color: "border-green-300 bg-green-50 text-green-700",
      description: "Tingkat trait yang tinggi"
    };
  }
}

// Helper function to get trait icon
function getTraitIcon(trait: string): string {
  const icons = {
    O: "🎨",
    C: "📋",
    E: "🎉",
    A: "🤝",
    N: "😰",
  };
  return icons[trait as keyof typeof icons] || "📊";
}

export default function OceanChart({ scores }: OceanChartProps) {
  // Find the highest scoring trait(s)
  const maxScore = Math.max(...Object.values(scores));
  const dominantTraits = Object.entries(scores)
    .filter(([, score]) => score === maxScore)
    .map(([trait]) => trait);

  return (
    <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
        Profil Kepribadian Big Five (OCEAN)
      </h2>

      {/* Chart and Summary Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Chart */}
        <div className="flex flex-col">
          <h3 className="text-lg font-medium text-gray-700 mb-4 text-center">Visualisasi Skor</h3>
          <OceanRadarChart scores={scores} />
        </div>

        {/* Summary Cards */}
        <div className="flex flex-col">
          <h3 className="text-lg font-medium text-gray-700 mb-4 text-center">Ringkasan Tingkat Trait</h3>
          <div className="grid grid-cols-1 gap-3 h-fit">
            {Object.entries(scores).map(([trait, score]) => {
              const description = oceanDescriptions.find(d => d.type === trait);
              const isHighest = dominantTraits.includes(trait);
              const traitLevel = getTraitLevel(score);
              return (
                <div
                  key={trait}
                  className={`p-4 rounded-lg border-2 transition-all hover:shadow-md ${
                    isHighest
                      ? 'border-green-500 bg-green-50 shadow-sm'
                      : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`text-lg mr-3 ${
                        isHighest ? 'text-green-600' : 'text-gray-700'
                      }`}>
                        {getTraitIcon(trait)}
                      </div>
                      <div>
                        <div className={`text-sm font-semibold ${
                          isHighest ? 'text-green-600' : 'text-gray-600'
                        }`}>
                          {description?.name} ({trait})
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${traitLevel.color}`}>
                        {traitLevel.level}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Compact Trait Explanations */}
      <div className="border-t pt-4">
        <h3 className="text-lg font-medium text-gray-700 mb-3 text-center">Penjelasan Trait OCEAN</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {oceanDescriptions.map((desc) => {
            const score = scores[desc.type];
            const isHighest = dominantTraits.includes(desc.type);
            const traitLevel = getTraitLevel(score);
            return (
              <div
                key={desc.type}
                className={`p-3 rounded-lg border transition-all hover:shadow-sm ${
                  isHighest
                    ? 'border-green-300 bg-green-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`text-sm font-semibold ${
                    isHighest ? 'text-green-700' : 'text-gray-700'
                  }`}>
                    {getTraitIcon(desc.type)} {desc.name} ({desc.type})
                  </h4>
                  <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                    isHighest
                      ? 'bg-green-200 text-green-800'
                      : 'bg-gray-200 text-gray-700'
                  }`}>
                    {traitLevel.level === 'Tinggi' ? '🔥' : traitLevel.level === 'Sedang' ? '⚡' : '💤'}
                  </span>
                </div>
                <p className="text-xs text-gray-600 leading-relaxed line-clamp-3">
                  {desc.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Compact Additional Info */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center">
            <span className="text-blue-500 text-lg mr-2">💡</span>
            <div>
              <span className="text-xs font-semibold text-blue-800">Big Five (OCEAN): </span>
              <span className="text-xs text-blue-700">
                Lima trait kepribadian universal yang menjelaskan pola perilaku dan preferensi seseorang.
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
