'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getShuffledQuestions } from '@/lib/riasecQuestions';
import { getShuffledOceanQuestions } from '@/lib/oceanQuestions';
import { calculateOceanScores } from '@/lib/profileStore';
import { RiasecScores, OceanScores, Question, OceanQuestion } from '@/lib/types';
import ProgressBar from '@/components/ProgressBar';
import QuestionCard from '@/components/QuestionCard';
import LikertScaleInput from '@/components/LikertScaleInput';
import NavigationButtons from '@/components/NavigationButtons';

// Constants
const ANSWER_DELAY = 100; // milliseconds



export default function TestPage() {
  const router = useRouter();
  
  // State management
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [questionId: number]: number }>({});
  const [allQuestions, setAllQuestions] = useState<(Question | OceanQuestion)[]>([]);
  const [riasecQuestions, setRiasecQuestions] = useState<Question[]>([]);
  const [oceanQuestions, setOceanQuestions] = useState<OceanQuestion[]>([]);
  const [showNextButton, setShowNextButton] = useState(false);

  // Initialize shuffled questions on component mount
  useEffect(() => {
    const shuffledRiasec = getShuffledQuestions();
    const shuffledOcean = getShuffledOceanQuestions();

    setRiasecQuestions(shuffledRiasec);
    setOceanQuestions(shuffledOcean);
    setAllQuestions([...shuffledRiasec, ...shuffledOcean]);
  }, []);

  // Check if current question was previously answered and show next button with delay
  useEffect(() => {
    if (allQuestions.length > 0) {
      const currentQuestion = allQuestions[currentQuestionIndex];
      const isAnswered = answers[currentQuestion.id] !== undefined;

      if (isAnswered) {
        // Show next button with delay for previously answered questions
        const timer = setTimeout(() => {
          setShowNextButton(true);
        }, ANSWER_DELAY);

        return () => clearTimeout(timer);
      } else {
        // Hide next button immediately for new questions
        setShowNextButton(false);
      }
    }
  }, [currentQuestionIndex, answers, allQuestions]);

  // Loading state
  if (allQuestions.length === 0) {
    return <LoadingScreen />;
  }

  // Computed values
  const currentQuestion = allQuestions[currentQuestionIndex];
  const totalQuestions = allQuestions.length;
  const riasecCount = riasecQuestions.length;
  const isRiasecPhase = currentQuestionIndex < riasecCount;
  const phaseProgress = isRiasecPhase 
    ? currentQuestionIndex + 1 
    : currentQuestionIndex - riasecCount + 1;
  const phaseTotal = isRiasecPhase ? riasecCount : oceanQuestions.length;

  // Event handlers
  const handleAnswerSelect = (questionId: number, value: number) => {
    const wasAlreadyAnswered = answers[questionId] !== undefined;

    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));

    // Auto-advance only if this is a new answer (not previously answered)
    if (!wasAlreadyAnswered) {
      setTimeout(() => {
        if (currentQuestionIndex < totalQuestions - 1) {
          setCurrentQuestionIndex(prev => prev + 1);
        } else {
          // Handle test completion
          const finalAnswers = {
            ...answers,
            [questionId]: value
          };
          const riasecScores = calculateRiasecScores(finalAnswers);
          const oceanScores = calculateOceanScores(finalAnswers);
          navigateToResults(riasecScores, oceanScores);
        }
      }, ANSWER_DELAY);
    }
  };



  const calculateRiasecScores = (finalAnswers: { [questionId: number]: number }): RiasecScores => {
    const riasecScores: RiasecScores = {
      R: 0,
      I: 0,
      A: 0,
      S: 0,
      E: 0,
      C: 0
    };

    riasecQuestions.forEach(question => {
      const answer = finalAnswers[question.id];
      if (answer !== undefined) {
        riasecScores[question.riasec_type] += answer;
      }
    });

    return riasecScores;
  };

  const navigateToResults = (riasecScores: RiasecScores, oceanScores: OceanScores) => {
    const queryParams = new URLSearchParams({
      // RIASEC scores
      r: riasecScores.R.toString(),
      i: riasecScores.I.toString(),
      a: riasecScores.A.toString(),
      s: riasecScores.S.toString(),
      e: riasecScores.E.toString(),
      c: riasecScores.C.toString(),
      // OCEAN scores
      o: oceanScores.O.toString(),
      ocean_c: oceanScores.C.toString(),
      ocean_e: oceanScores.E.toString(),
      ocean_a: oceanScores.A.toString(),
      n: oceanScores.N.toString()
    });

    router.push(`/result?${queryParams.toString()}`);
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      // Handle test completion
      const finalAnswers = answers;
      const riasecScores = calculateRiasecScores(finalAnswers);
      const oceanScores = calculateOceanScores(finalAnswers);
      navigateToResults(riasecScores, oceanScores);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-3xl mx-auto">
        <TestHeader 
          isRiasecPhase={isRiasecPhase}
          phaseProgress={phaseProgress}
          phaseTotal={phaseTotal}
        />

        <ProgressBar 
          current={currentQuestionIndex + 1} 
          total={totalQuestions} 
        />

        <QuestionCard
          question={currentQuestion}
          questionNumber={currentQuestionIndex + 1}
          totalQuestions={totalQuestions}
        />

        <div className="bg-white rounded-xl shadow-lg p-8">
          <LikertScaleInput
            questionId={currentQuestion.id}
            selectedValue={answers[currentQuestion.id]}
            onSelect={handleAnswerSelect}
          />

          <NavigationButtons
            currentIndex={currentQuestionIndex}
            totalQuestions={totalQuestions}
            canGoNext={showNextButton}
            onPrevious={handlePrevious}
            onNext={handleNext}
          />
        </div>
      </div>
    </div>
  );
}

// Sub-components
function LoadingScreen() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Mempersiapkan pertanyaan...</p>
      </div>
    </div>
  );
}

interface TestHeaderProps {
  isRiasecPhase: boolean;
  phaseProgress: number;
  phaseTotal: number;
}

function TestHeader({ isRiasecPhase }: TestHeaderProps) {
  return (
    <div className="text-center mb-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-2">
        {isRiasecPhase ? 'Tes Minat Karir RIASEC' : 'Tes Kepribadian Big Five (OCEAN)'}
      </h1>
      <p className="text-gray-600 mb-2">
        {isRiasecPhase
          ? 'Jawab setiap pertanyaan sesuai dengan minat dan preferensi karir Anda'
          : 'Jawab setiap pertanyaan sesuai dengan kepribadian dan karakteristik Anda'
        }
      </p>
    </div>
  );
}

