'use client';

import { RiasecScores } from '@/lib/types';
import { riasecDescriptions } from '@/lib/riasecQuestions';
import { getRiasecScoreLevel } from '@/lib/levelVisualization';
import RadarChart from '@/components/RadarChart';

interface RiasecChartProps {
  scores: RiasecScores;
}

export default function RiasecChart({ scores }: RiasecChartProps) {
  // Find the highest scoring type(s)
  const maxScore = Math.max(...Object.values(scores));
  const dominantTypes = Object.entries(scores)
    .filter(([, score]) => score === maxScore)
    .map(([type]) => type);

  return (
    <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
        Profil <PERSON>t <PERSON>r (RIASEC)
      </h2>
      
      {/* Chart and Summary Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Chart */}
        <div className="flex flex-col">
          <h3 className="text-lg font-medium text-gray-700 mb-4 text-center">Visualisasi Skor</h3>
          <RadarChart scores={scores} />
        </div>
        
        {/* Summary Cards */}
        <div className="flex flex-col">
          <h3 className="text-lg font-medium text-gray-700 mb-4 text-center">Ringkasan Tingkat Minat</h3>
          <div className="grid grid-cols-2 gap-3 h-fit">
            {Object.entries(scores).map(([type, score]) => {
              const isHighest = dominantTypes.includes(type);
              const scoreLevel = getRiasecScoreLevel(score);
              return (
                <div
                  key={type}
                  className={`p-4 rounded-lg border-2 transition-all hover:shadow-md ${
                    isHighest
                      ? 'border-indigo-500 bg-indigo-50 shadow-sm'
                      : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className={`text-lg mb-2 ${
                      isHighest ? 'text-indigo-600' : 'text-gray-700'
                    }`}>
                      {scoreLevel.icon}
                    </div>
                    <div className={`text-sm font-semibold mb-2 ${
                      isHighest ? 'text-indigo-600' : 'text-gray-600'
                    }`}>
                      {type}
                    </div>
                    <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${scoreLevel.color}`}>
                      {scoreLevel.level}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Compact Type Explanations */}
      <div className="border-t pt-4">
        <h3 className="text-lg font-medium text-gray-700 mb-3 text-center">Penjelasan Tipe RIASEC</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {riasecDescriptions.map((desc) => {
            const score = scores[desc.type];
            const isHighest = dominantTypes.includes(desc.type);
            const scoreLevel = getRiasecScoreLevel(score);
            return (
              <div
                key={desc.type}
                className={`p-3 rounded-lg border transition-all hover:shadow-sm ${
                  isHighest
                    ? 'border-indigo-300 bg-indigo-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`text-sm font-semibold ${
                    isHighest ? 'text-indigo-700' : 'text-gray-700'
                  }`}>
                    {desc.name} ({desc.type})
                  </h4>
                  <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                    isHighest
                      ? 'bg-indigo-200 text-indigo-800'
                      : 'bg-gray-200 text-gray-700'
                  }`}>
                    {scoreLevel.icon}
                  </span>
                </div>
                <p className="text-xs text-gray-600 leading-relaxed line-clamp-3">
                  {desc.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
