// Standar visualisasi tingkat untuk RIASEC dan <PERSON>
// Menyediakan konsistensi icon, warna, dan deskripsi untuk semua komponen

export type LevelType = 'Sangat Rendah' | 'Rendah' | 'Sedang' | 'Tinggi' | 'Sangat Tinggi';

export interface LevelVisualization {
  level: LevelType;
  icon: string;
  color: string;
  bgColor: string;
  borderColor: string;
  textColor: string;
  description: string;
}

// Standar visualisasi untuk setiap tingkat
export const LEVEL_VISUALIZATIONS: Record<LevelType, LevelVisualization> = {
  'Sangat Rendah': {
    level: 'Sangat Rendah',
    icon: '❄️',
    color: 'text-slate-600 bg-slate-50 border-slate-200',
    bgColor: 'bg-slate-50',
    borderColor: 'border-slate-200',
    textColor: 'text-slate-600',
    description: 'Tingkat yang sangat rendah'
  },
  'Rendah': {
    level: 'Rendah',
    icon: '💤',
    color: 'text-gray-600 bg-gray-50 border-gray-200',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    textColor: 'text-gray-600',
    description: 'Tingkat yang rendah'
  },
  'Sedang': {
    level: 'Sedang',
    icon: '⚡',
    color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-600',
    description: 'Tingkat yang moderat'
  },
  'Tinggi': {
    level: 'Tinggi',
    icon: '🔥',
    color: 'text-blue-600 bg-blue-50 border-blue-200',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-600',
    description: 'Tingkat yang tinggi'
  },
  'Sangat Tinggi': {
    level: 'Sangat Tinggi',
    icon: '🚀',
    color: 'text-green-600 bg-green-50 border-green-200',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    textColor: 'text-green-600',
    description: 'Tingkat yang sangat tinggi'
  }
};

// Fungsi untuk mendapatkan visualisasi berdasarkan level
export function getLevelVisualization(level: LevelType): LevelVisualization {
  return LEVEL_VISUALIZATIONS[level];
}

// Fungsi untuk mendapatkan level RIASEC berdasarkan skor
export function getRiasecScoreLevel(score: number): { level: string; color: string; description: string; icon: string } {
  let levelType: LevelType;
  let description: string;

  if (score >= 20) {
    levelType = 'Sangat Tinggi';
    description = 'Minat yang sangat kuat pada area ini';
  } else if (score >= 15) {
    levelType = 'Tinggi';
    description = 'Minat yang cukup kuat pada area ini';
  } else if (score >= 10) {
    levelType = 'Sedang';
    description = 'Minat yang moderat pada area ini';
  } else if (score >= 5) {
    levelType = 'Rendah';
    description = 'Minat yang rendah pada area ini';
  } else {
    levelType = 'Sangat Rendah';
    description = 'Minat yang sangat rendah pada area ini';
  }

  const visualization = getLevelVisualization(levelType);
  
  return {
    level: visualization.level,
    color: visualization.color,
    description,
    icon: visualization.icon
  };
}

// Fungsi untuk mendapatkan level OCEAN berdasarkan skor
export function getOceanScoreLevel(score: number): { level: 'Rendah' | 'Sedang' | 'Tinggi'; color: string; description: string; icon: string } {
  let levelType: LevelType;
  let description: string;

  // OCEAN scores range from 5-25 (5 questions × 1-5 scale)
  if (score >= 22) {
    levelType = 'Sangat Tinggi';
    description = 'Tingkat trait yang sangat tinggi';
  } else if (score >= 19) {
    levelType = 'Tinggi';
    description = 'Tingkat trait yang tinggi';
  } else if (score >= 13) {
    levelType = 'Sedang';
    description = 'Tingkat trait yang sedang';
  } else if (score >= 8) {
    levelType = 'Rendah';
    description = 'Tingkat trait yang rendah';
  } else {
    levelType = 'Sangat Rendah';
    description = 'Tingkat trait yang sangat rendah';
  }

  const visualization = getLevelVisualization(levelType);
  
  // Untuk kompatibilitas dengan interface yang ada, kita mapping ke 3 level
  let mappedLevel: 'Rendah' | 'Sedang' | 'Tinggi';
  if (levelType === 'Sangat Rendah' || levelType === 'Rendah') {
    mappedLevel = 'Rendah';
  } else if (levelType === 'Sedang') {
    mappedLevel = 'Sedang';
  } else {
    mappedLevel = 'Tinggi';
  }

  return {
    level: mappedLevel,
    color: visualization.color,
    description,
    icon: visualization.icon
  };
}

// Fungsi helper untuk mendapatkan icon berdasarkan level string
export function getLevelIcon(level: string): string {
  switch (level) {
    case 'Sangat Tinggi':
      return LEVEL_VISUALIZATIONS['Sangat Tinggi'].icon;
    case 'Tinggi':
      return LEVEL_VISUALIZATIONS['Tinggi'].icon;
    case 'Sedang':
      return LEVEL_VISUALIZATIONS['Sedang'].icon;
    case 'Rendah':
      return LEVEL_VISUALIZATIONS['Rendah'].icon;
    case 'Sangat Rendah':
      return LEVEL_VISUALIZATIONS['Sangat Rendah'].icon;
    default:
      return LEVEL_VISUALIZATIONS['Sedang'].icon;
  }
}

// Fungsi helper untuk mendapatkan warna berdasarkan level string
export function getLevelColor(level: string): string {
  switch (level) {
    case 'Sangat Tinggi':
      return LEVEL_VISUALIZATIONS['Sangat Tinggi'].color;
    case 'Tinggi':
      return LEVEL_VISUALIZATIONS['Tinggi'].color;
    case 'Sedang':
      return LEVEL_VISUALIZATIONS['Sedang'].color;
    case 'Rendah':
      return LEVEL_VISUALIZATIONS['Rendah'].color;
    case 'Sangat Rendah':
      return LEVEL_VISUALIZATIONS['Sangat Rendah'].color;
    default:
      return LEVEL_VISUALIZATIONS['Sedang'].color;
  }
}
